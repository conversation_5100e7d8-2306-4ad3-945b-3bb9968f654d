#!/usr/bin/env python3

import pandas as pd
import json

def analyze_specific_cnombre():
    """Analizar específicamente el USERHISTID que genera CNOMBRE en el original"""
    
    # USERHISTID del archivo original que tiene CNOMBRE con nombres reales
    target_userhistid = "7716469503345"
    
    # Cargar datos del parquet original
    parquet_path = "/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/S3_LOG_USER/output/20250610/LOG_USR.parquet"
    df = pd.read_parquet(parquet_path)
    
    print(f"📊 Datos cargados: {len(df):,} registros")
    
    # Buscar el registro específico
    target_row = df[df['USERHISTID'] == target_userhistid]
    
    if len(target_row) == 0:
        print(f"❌ No se encontró USERHISTID {target_userhistid}")
        return
    
    print(f"✅ Encontrado USERHISTID {target_userhistid}")
    
    for idx, row in target_row.iterrows():
        print(f"\n📝 USERHISTID: {row['USERHISTID']}")
        print(f"📝 REQUESTTYPE: {row['REQUESTTYPE']}")
        print(f"📝 CREATEDON: {row['CREATEDON']}")
        print(f"📝 DOCUMENTO: {row['DOCUMENTO']}")
        
        old_data = row['OLDDATA']
        new_data = row['NEWDATA']
        
        if old_data:
            print(f"\n📝 OLD_DATA completo:")
            print(old_data)
            
            try:
                old_json = json.loads(old_data)
                print(f"\n📝 OLD_JSON parseado exitosamente")
                print(f"📝 Keys en OLD_JSON: {list(old_json.keys())}")
                
                # Buscar firstName y lastName recursivamente
                def find_names_in_json(data, path="root"):
                    found = []
                    if isinstance(data, dict):
                        for key, value in data.items():
                            if key == 'firstName':
                                found.append(f"{path}.firstName = {value}")
                            elif key == 'lastName':
                                found.append(f"{path}.lastName = {value}")
                            elif isinstance(value, (dict, list)):
                                found.extend(find_names_in_json(value, f"{path}.{key}"))
                    elif isinstance(data, list):
                        for i, item in enumerate(data):
                            found.extend(find_names_in_json(item, f"{path}[{i}]"))
                    return found
                
                old_names = find_names_in_json(old_json)
                print(f"📝 Nombres encontrados en OLD_JSON:")
                for name in old_names:
                    print(f"  - {name}")
                    
            except Exception as e:
                print(f"❌ Error parseando OLD_JSON: {e}")
        
        if new_data:
            print(f"\n📝 NEW_DATA completo:")
            print(new_data)
            
            try:
                new_json = json.loads(new_data)
                print(f"\n📝 NEW_JSON parseado exitosamente")
                print(f"📝 Keys en NEW_JSON: {list(new_json.keys())}")
                
                new_names = find_names_in_json(new_json)
                print(f"📝 Nombres encontrados en NEW_JSON:")
                for name in new_names:
                    print(f"  - {name}")
                    
            except Exception as e:
                print(f"❌ Error parseando NEW_JSON: {e}")

if __name__ == '__main__':
    analyze_specific_cnombre()
