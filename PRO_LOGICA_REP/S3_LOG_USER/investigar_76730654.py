#!/usr/bin/env python3
import duckdb
import pandas as pd

# Conectar a DuckDB
conn = duckdb.connect()

print("🔍 INVESTIGACIÓN DETECTIVE EXTREMO - DOCUMENTO ********")
print("=" * 60)

# Investigar en LOG_USR.parquet (antes de procesar.py)
print("\n📄 PASO 1: Investigar en LOG_USR.parquet (datos de SP_LOG_USR)")
try:
    query = """
    SELECT 
        USERHISTID,
        CREATEDON,
        REQUESTTYPE,
        DOCUMENTO,
        MSISDN,
        BANKDOMAIN
    FROM read_parquet('output/********/LOG_USR.parquet')
    WHERE DOCUMENTO = '********'
    ORDER BY CREATEDON
    """
    result = conn.execute(query).fetchall()
    print(f"Registros encontrados en LOG_USR.parquet: {len(result)}")
    for i, row in enumerate(result):
        print(f"  {i+1}. USERHISTID: {row[0]}, CREATEDON: {row[1]}, REQUESTTYPE: {row[2]}")
except Exception as e:
    print(f"Error: {e}")

# Investigar en LOG_USR_ORACLE_LOGIC.parquet (después de lógica Oracle)
print("\n📄 PASO 2: Investigar en LOG_USR_ORACLE_LOGIC.parquet (después de lógica Oracle)")
try:
    query = """
    SELECT 
        USERHISTID,
        CREATEDON,
        REQUESTTYPE,
        DOCUMENTO,
        MSISDN,
        BANKDOMAIN
    FROM read_parquet('output/********/LOG_USR_ORACLE_LOGIC.parquet')
    WHERE DOCUMENTO = '********'
    ORDER BY CREATEDON
    """
    result = conn.execute(query).fetchall()
    print(f"Registros encontrados en LOG_USR_ORACLE_LOGIC.parquet: {len(result)}")
    for i, row in enumerate(result):
        print(f"  {i+1}. USERHISTID: {row[0]}, CREATEDON: {row[1]}, REQUESTTYPE: {row[2]}")
except Exception as e:
    print(f"Error: {e}")

print("\n🔍 CONCLUSIÓN:")
print("Si hay múltiples registros, Oracle está seleccionando uno diferente al nuestro")
print("Necesitamos revisar la lógica de deduplicación/selección")

