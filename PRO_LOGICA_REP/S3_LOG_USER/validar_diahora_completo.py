#!/usr/bin/env python3
import pandas as pd
import sys

def validar_diahora_completo():
    """Validación exhaustiva de la columna DiaHora entre Oracle y S3"""
    print("🔍 VALIDACIÓN EXHAUSTIVA COLUMNA DIAHORA")
    print("=" * 60)
    
    # Archivos a comparar
    archivo_oracle = "/home/<USER>/generate/log_usuarios/output/LOGUSR-FCOMPARTAMOS-20250610052953.csv"
    archivo_s3 = "./output/20250609/csv_exports/csv_final_procesado/LOGUSR-FCOMPARTAMOS-20250609220155.csv"
    
    try:
        print("📄 Cargando archivos...")
        
        # Cargar archivos CSV
        df_oracle = pd.read_csv(archivo_oracle, header=None, dtype=str)
        df_s3 = pd.read_csv(archivo_s3, header=None, dtype=str)
        
        print(f"   Oracle: {len(df_oracle)} registros")
        print(f"   S3: {len(df_s3)} registros")
        
        if len(df_oracle) != len(df_s3):
            print("❌ ERROR: Diferentes cantidades de registros")
            return False
        
        # Columnas relevantes (basado en estructura CSV)
        # 0=TipoTransaccion, 1=TransactionID, 2=DiaHora, 4=TipoDocumento, 5=Documento
        
        print("\n🔍 Comparando columna DiaHora por documento...")
        
        diferencias = []
        coincidencias = 0
        
        # Crear diccionarios para comparación rápida
        oracle_docs = {}
        s3_docs = {}
        
        # Procesar Oracle
        for idx, row in df_oracle.iterrows():
            documento = row[5]  # Columna Documento
            diahora = row[2]    # Columna DiaHora
            tipo_transaccion = row[0]  # TipoTransaccion
            
            key = f"{documento}_{tipo_transaccion}"
            if key not in oracle_docs:
                oracle_docs[key] = []
            oracle_docs[key].append((idx + 1, diahora))
        
        # Procesar S3
        for idx, row in df_s3.iterrows():
            documento = row[5]  # Columna Documento
            diahora = row[2]    # Columna DiaHora
            tipo_transaccion = row[0]  # TipoTransaccion
            
            key = f"{documento}_{tipo_transaccion}"
            if key not in s3_docs:
                s3_docs[key] = []
            s3_docs[key].append((idx + 1, diahora))
        
        print(f"\n📊 Documentos únicos en Oracle: {len(oracle_docs)}")
        print(f"📊 Documentos únicos en S3: {len(s3_docs)}")
        
        # Comparar documentos
        documentos_oracle = set(oracle_docs.keys())
        documentos_s3 = set(s3_docs.keys())
        
        documentos_comunes = documentos_oracle & documentos_s3
        documentos_solo_oracle = documentos_oracle - documentos_s3
        documentos_solo_s3 = documentos_s3 - documentos_oracle
        
        print(f"\n📋 Documentos comunes: {len(documentos_comunes)}")
        if documentos_solo_oracle:
            print(f"⚠️  Solo en Oracle: {len(documentos_solo_oracle)}")
        if documentos_solo_s3:
            print(f"⚠️  Solo en S3: {len(documentos_solo_s3)}")
        
        # Verificar timestamps para documentos comunes
        print(f"\n🕐 Verificando timestamps para documentos comunes...")
        
        diferencias_timestamp = []
        
        for doc_key in sorted(documentos_comunes):
            oracle_entries = oracle_docs[doc_key]
            s3_entries = s3_docs[doc_key]
            
            # Para documentos con múltiples entradas, comparar la primera
            oracle_timestamp = oracle_entries[0][1]
            s3_timestamp = s3_entries[0][1]
            
            if oracle_timestamp != s3_timestamp:
                documento = doc_key.split('_')[0]
                tipo = doc_key.split('_', 1)[1]
                diferencias_timestamp.append({
                    'documento': documento,
                    'tipo': tipo,
                    'oracle_timestamp': oracle_timestamp,
                    's3_timestamp': s3_timestamp,
                    'oracle_linea': oracle_entries[0][0],
                    's3_linea': s3_entries[0][0]
                })
            else:
                coincidencias += 1
        
        # Mostrar resultados
        print(f"\n📊 RESULTADOS DE VALIDACIÓN:")
        print(f"   ✅ Coincidencias exactas: {coincidencias:,}")
        print(f"   ❌ Diferencias encontradas: {len(diferencias_timestamp):,}")
        
        if diferencias_timestamp:
            print(f"\n⚠️  DIFERENCIAS EN TIMESTAMPS:")
            print("   Doc | Tipo | Oracle | S3 | Línea Oracle | Línea S3")
            print("   " + "-" * 70)
            
            for diff in diferencias_timestamp[:20]:  # Mostrar primeras 20
                print(f"   {diff['documento']:<8} | {diff['tipo']:<12} | {diff['oracle_timestamp']} | {diff['s3_timestamp']} | {diff['oracle_linea']:<4} | {diff['s3_linea']:<4}")
            
            if len(diferencias_timestamp) > 20:
                print(f"   ... y {len(diferencias_timestamp) - 20} diferencias más")
        
        # Calcular porcentaje de coincidencia
        total_comparaciones = len(documentos_comunes)
        porcentaje_coincidencia = (coincidencias / total_comparaciones * 100) if total_comparaciones > 0 else 0
        
        print(f"\n🎯 PORCENTAJE DE COINCIDENCIA: {porcentaje_coincidencia:.2f}%")
        
        if porcentaje_coincidencia == 100.0:
            print("🎉 ¡HOMOLOGACIÓN PERFECTA! Todos los timestamps coinciden")
            return True
        else:
            print(f"⚠️  Se requiere investigación adicional para {len(diferencias_timestamp)} casos")
            return False
            
    except Exception as e:
        print(f"❌ Error en validación: {e}")
        return False

if __name__ == "__main__":
    validar_diahora_completo()
