#!/usr/bin/env python3
"""
TEST ESPECÍFICO: Análisis puntual del problema de mapeo de columnas
Objetivo: Identificar exactamente dónde falla el mapeo entre REQUESTTYPE y TipoTransaccion
"""

import pandas as pd
import sys
import os

def test_original_vs_logica():
    """
    Comparar archivo original vs LOGICA paso a paso
    """
    print("🔍 TEST ESPECÍFICO: MAPEO DE COLUMNAS")
    print("=" * 60)
    
    # 1. CARGAR ARCHIVO ORIGINAL
    print("\n1️⃣ CARGANDO ARCHIVO ORIGINAL...")
    try:
        original_path = '/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/S3_LOG_USER/output/20250610/LOG_USR.parquet'
        df_original = pd.read_parquet(original_path)
        print(f"✅ Original cargado: {len(df_original):,} registros, {len(df_original.columns)} columnas")
        print(f"📋 Columnas originales: {list(df_original.columns)}")
        
        # Verificar REQUESTTYPE en original
        if 'REQUESTTYPE' in df_original.columns:
            print(f"\n🔍 REQUESTTYPE en original:")
            tipos_original = df_original['REQUESTTYPE'].value_counts()
            print(tipos_original.head(10))
        else:
            print("❌ REQUESTTYPE no existe en original")
            
    except Exception as e:
        print(f"❌ Error cargando original: {e}")
        return
    
    # 2. CARGAR ARCHIVO LOGICA
    print("\n2️⃣ CARGANDO ARCHIVO LOGICA...")
    try:
        logica_path = '/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/LOGICA/output/temp_deduplicated/LOG_USR_DEDUPLICATED.parquet'
        df_logica = pd.read_parquet(logica_path)
        print(f"✅ LOGICA cargado: {len(df_logica):,} registros, {len(df_logica.columns)} columnas")
        print(f"📋 Columnas LOGICA: {list(df_logica.columns)}")
        
        # Verificar REQUESTTYPE en LOGICA
        if 'REQUESTTYPE' in df_logica.columns:
            print(f"\n🔍 REQUESTTYPE en LOGICA:")
            tipos_logica = df_logica['REQUESTTYPE'].value_counts()
            print(tipos_logica.head(10))
        else:
            print("❌ REQUESTTYPE no existe en LOGICA")
            
    except Exception as e:
        print(f"❌ Error cargando LOGICA: {e}")
        return
    
    # 3. COMPARAR REQUESTTYPE
    print("\n3️⃣ COMPARANDO REQUESTTYPE...")
    if 'REQUESTTYPE' in df_original.columns and 'REQUESTTYPE' in df_logica.columns:
        tipos_orig = set(df_original['REQUESTTYPE'].unique())
        tipos_log = set(df_logica['REQUESTTYPE'].unique())
        
        print(f"🔍 Tipos en ORIGINAL: {tipos_orig}")
        print(f"🔍 Tipos en LOGICA: {tipos_log}")
        print(f"🔍 Tipos COMUNES: {tipos_orig & tipos_log}")
        print(f"🔍 Solo en ORIGINAL: {tipos_orig - tipos_log}")
        print(f"🔍 Solo en LOGICA: {tipos_log - tipos_orig}")
    
    # 4. SIMULAR MAPEO ORIGINAL
    print("\n4️⃣ SIMULANDO MAPEO ORIGINAL...")
    
    # Mapeo del archivo original (líneas 27-44)
    request_type_mapping = {
        "Suspend User": "BLOQUSR",
        "Lock Wallet": "BLOQCTA", 
        "Unlock Wallet": "DESBLCTA",
        "Resume User": "DESBUSR",
        "CHANGE_AUTH_FACTOR": "CPIN",
        "RESET_AUTH_VALUE": "RPIN",
        "ActivateUser": "ACTIVA",
        "ActivateCuenta": "AGRCTA",
        "AfiliaUser": "AFILIA",
        "ClosedUserAccount": "CUSR",
        "ClosedAccount": "CCUENTA"
    }
    
    # Aplicar mapeo a LOGICA
    df_test = df_logica.copy()
    df_test['TIPOTRANSACCION'] = df_test['REQUESTTYPE'].map(request_type_mapping).fillna(df_test['REQUESTTYPE'])
    
    print(f"🔍 TIPOTRANSACCION después del mapeo:")
    tipos_mapeados = df_test['TIPOTRANSACCION'].value_counts()
    print(tipos_mapeados.head(10))
    
    # 5. APLICAR COLUMN_MAPPING
    print("\n5️⃣ APLICANDO COLUMN_MAPPING...")
    
    # Column mapping del original (líneas 118-148)
    column_mapping = {
        'TIPOTRANSACCION': 'TipoTransaccion',
        'CREATEDON': 'DiaHora',
        'ACCOUNTTYPE': 'TipoCuenta',
        'TIPODOCUMENTO': 'TipoDocumento',
        'DOCUMENTO': 'Documento',
        'MSISDN': 'MSISDN',
        'BANKDOMAIN': 'BankDomain',
        'NOMBRE': 'Nombres',
        'APELLIDO': 'Apellidos',
        'PERFILA': 'PerfilA',
        'PERFILB': 'PerfilB',
        'IDIOMAA': 'IdiomaA',
        'IDIOMAB': 'IdiomaB',
        'TELCOA': 'TelcoA',
        'TELCOB': 'TelcoB',
        'RAZON': 'Razon',
        'CREATED_BY': 'Initiating User',
        'MSISDNB': 'MSISDNB',
        'NNOMBRE': 'NNombre',
        'NAPELLIDO': 'NApellido',
        'USERID': 'ID USUARIO',
        'ACCOUNTID': 'ID CUENTA',
        'PERFILCUENTA': 'PerfilCuenta',
        'PERFILCUENTAA': 'PerfilCuentaA',
        'PERFILCUENTAB': 'PerfilCuentaB',
        'TIPODOCUMENTOA': 'TipoDocumentoA',
        'TIPODOCUMENTOB': 'TipoDocumentoB',
        'DOCUMENTOB': 'NumDocumentoA',
        'NUMDOCUMENTOB': 'NumDocumentoB'
    }
    
    # Aplicar column mapping
    df_test = df_test.rename(columns=column_mapping)
    
    print(f"🔍 Columnas después del column_mapping:")
    print(f"📋 Primeras 10: {list(df_test.columns)[:10]}")
    
    # Verificar si TipoTransaccion existe
    if 'TipoTransaccion' in df_test.columns:
        print(f"\n✅ TipoTransaccion EXISTE después del mapeo!")
        tipos_finales = df_test['TipoTransaccion'].value_counts()
        print(f"🔍 TipoTransaccion valores:")
        print(tipos_finales.head(10))
    else:
        print(f"\n❌ TipoTransaccion NO EXISTE después del mapeo!")
        print(f"🔍 Columnas que contienen 'tipo' o 'transac': {[c for c in df_test.columns if 'tipo' in c.lower() or 'transac' in c.lower()]}")
    
    # 6. GENERAR ARCHIVO DE TEST
    print("\n6️⃣ GENERANDO ARCHIVO DE TEST...")
    
    # Generar TransactionID
    df_test['TransactionID'] = range(1, len(df_test) + 1)
    
    # Reordenar columnas como el original
    final_column_order = [
        'TipoTransaccion', 'TransactionID', 'DiaHora', 'TipoCuenta',
        'TipoDocumento', 'Documento', 'MSISDN', 'BankDomain',
        'Nombres', 'Apellidos', 'PerfilA', 'PerfilB',
        'IdiomaA', 'IdiomaB', 'TelcoA', 'TelcoB', 'Razon',
        'Initiating User', 'MSISDNB', 'NNombre', 'NApellido',
        'ID USUARIO', 'ID CUENTA', 'PerfilCuenta', 'PerfilCuentaA',
        'PerfilCuentaB', 'TipoDocumentoA', 'TipoDocumentoB',
        'NumDocumentoA', 'NumDocumentoB'
    ]
    
    # Verificar qué columnas existen
    existing_columns = [col for col in final_column_order if col in df_test.columns]
    missing_columns = [col for col in final_column_order if col not in df_test.columns]
    
    print(f"🔍 Columnas existentes: {len(existing_columns)}/30")
    print(f"🔍 Columnas faltantes: {missing_columns}")
    
    if existing_columns:
        df_final = df_test[existing_columns]
        
        # Guardar archivo de test
        test_output = 'test_column_mapping_output.csv'
        df_final.to_csv(test_output, index=False, header=False)
        print(f"✅ Archivo de test generado: {test_output}")
        print(f"📊 Registros: {len(df_final)}, Columnas: {len(df_final.columns)}")
        
        # Verificar primera columna
        if len(df_final.columns) > 0:
            primera_col = df_final.iloc[:, 0]
            print(f"🔍 Primera columna (debe ser TipoTransaccion):")
            print(primera_col.value_counts().head(5))
    
    print("\n🎯 ANÁLISIS COMPLETADO")

if __name__ == "__main__":
    test_original_vs_logica()
