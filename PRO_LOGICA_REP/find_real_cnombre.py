#!/usr/bin/env python3

import pandas as pd
import json

def find_real_cnombre_candidates():
    """Encontrar registros que realmente tienen firstName y lastName con valores"""
    
    # Cargar datos del parquet original
    parquet_path = "/home/<USER>/aws/REP/reports/generate_nv/PRO_LOGICA_REP/S3_LOG_USER/output/20250610/LOG_USR.parquet"
    df = pd.read_parquet(parquet_path)
    
    print(f"📊 Datos cargados: {len(df):,} registros")
    
    # Buscar registros con firstName y lastName que NO sean None
    candidates = []
    
    for idx, row in df.iterrows():
        if row['REQUESTTYPE'] == 'User Modification' and row['OLDDATA'] and row['NEWDATA']:
            try:
                old_json = json.loads(row['OLDDATA'])
                new_json = json.loads(row['NEWDATA'])
                
                # Buscar firstName y lastName recursivamente
                def find_real_names(data):
                    names = {}
                    if isinstance(data, dict):
                        for key, value in data.items():
                            if key == 'firstName' and value is not None and value != '':
                                names['firstName'] = value
                            elif key == 'lastName' and value is not None and value != '':
                                names['lastName'] = value
                            elif isinstance(value, (dict, list)):
                                nested_names = find_real_names(value)
                                names.update(nested_names)
                    elif isinstance(data, list):
                        for item in data:
                            nested_names = find_real_names(item)
                            names.update(nested_names)
                    return names
                
                old_names = find_real_names(old_json)
                new_names = find_real_names(new_json)
                
                if old_names or new_names:
                    candidates.append({
                        'USERHISTID': row['USERHISTID'],
                        'old_firstName': old_names.get('firstName'),
                        'old_lastName': old_names.get('lastName'),
                        'new_firstName': new_names.get('firstName'),
                        'new_lastName': new_names.get('lastName'),
                        'DOCUMENTO': row['DOCUMENTO']
                    })
                    
                    if len(candidates) >= 10:  # Solo primeros 10
                        break
                        
            except Exception as e:
                continue
    
    print(f"\n✅ Encontrados {len(candidates)} candidatos con nombres reales:")
    for candidate in candidates:
        print(f"📝 USERHISTID: {candidate['USERHISTID']}")
        print(f"   OLD: {candidate['old_firstName']} {candidate['old_lastName']}")
        print(f"   NEW: {candidate['new_firstName']} {candidate['new_lastName']}")
        print(f"   DOCUMENTO: {candidate['DOCUMENTO']}")
        print()

if __name__ == '__main__':
    find_real_cnombre_candidates()
